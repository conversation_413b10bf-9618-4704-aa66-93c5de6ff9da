"""
配置模型定义。

使用 Pydantic 定义应用程序的配置结构，支持类型验证和环境变量加载。
"""

from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class LogLevel(str, Enum):
    """日志级别枚举。"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LLMProvider(str, Enum):
    """LLM 服务提供商枚举。"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LOCAL = "local"


class LanguageCode(str, Enum):
    """支持的语言代码枚举。"""
    AUTO = "auto"
    EN = "en"
    ZH = "zh"
    JA = "ja"
    KO = "ko"
    FR = "fr"
    DE = "de"
    ES = "es"
    RU = "ru"


class AppConfig(BaseModel):
    """应用程序基础配置。"""
    name: str = "PDF Translator"
    version: str = "0.1.0"
    debug: bool = False
    log_level: LogLevel = LogLevel.INFO


class QualityConfig(BaseModel):
    """翻译质量配置。"""
    min_score: float = Field(0.8, ge=0.0, le=1.0, description="最低质量分数")
    max_retries: int = Field(3, ge=1, le=10, description="最大重试次数")
    context_window: int = Field(2000, ge=100, description="上下文窗口大小")


class BatchConfig(BaseModel):
    """批处理配置。"""
    max_concurrent: int = Field(3, ge=1, le=10, description="最大并发数")
    chunk_size: int = Field(1000, ge=100, description="文本块大小")


class TranslationConfig(BaseModel):
    """翻译配置。"""
    default_source_lang: LanguageCode = LanguageCode.AUTO
    default_target_lang: LanguageCode = LanguageCode.ZH
    supported_languages: List[LanguageCode] = [
        LanguageCode.EN, LanguageCode.ZH, LanguageCode.JA,
        LanguageCode.KO, LanguageCode.FR, LanguageCode.DE,
        LanguageCode.ES, LanguageCode.RU
    ]
    quality: QualityConfig = QualityConfig()
    batch: BatchConfig = BatchConfig()


class LLMConfig(BaseModel):
    """LLM 服务配置。"""
    model: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    temperature: float = Field(0.1, ge=0.0, le=2.0)
    max_tokens: int = Field(4000, ge=1)


class LLMSettings(BaseModel):
    """LLM 服务设置。"""
    default_provider: LLMProvider = LLMProvider.OPENAI
    openai: LLMConfig = LLMConfig(
        model="gpt-4-turbo-preview",
        base_url="https://api.openai.com/v1"
    )
    anthropic: LLMConfig = LLMConfig(
        model="claude-3-sonnet-20240229"
    )
    local: LLMConfig = LLMConfig(
        model="local_model"
    )


class ParsingConfig(BaseModel):
    """PDF 解析配置。"""
    extract_images: bool = True
    extract_tables: bool = True
    preserve_layout: bool = True
    dpi: int = Field(300, ge=72, le=600, description="图像提取DPI")


class OCRConfig(BaseModel):
    """OCR 配置。"""
    enabled: bool = True
    language: str = "eng+chi_sim"
    confidence_threshold: int = Field(60, ge=0, le=100, description="置信度阈值")


class OutputConfig(BaseModel):
    """PDF 输出配置。"""
    preserve_fonts: bool = True
    preserve_colors: bool = True
    preserve_images: bool = True
    compression: str = "deflate"
    quality: int = Field(95, ge=1, le=100)


class PDFConfig(BaseModel):
    """PDF 处理配置。"""
    parsing: ParsingConfig = ParsingConfig()
    ocr: OCRConfig = OCRConfig()
    output: OutputConfig = OutputConfig()


class ImageConfig(BaseModel):
    """图像处理配置。"""
    supported_formats: List[str] = ["png", "jpg", "jpeg", "bmp", "tiff"]
    max_size: List[int] = [2048, 2048]
    enhance_contrast: bool = True
    denoise: bool = True


class CacheConfig(BaseModel):
    """缓存配置。"""
    enabled: bool = True
    ttl: int = Field(3600, ge=60, description="缓存时间（秒）")
    max_size: int = Field(1000, ge=10, description="最大缓存条目数")


class LoggingConfig(BaseModel):
    """日志配置。"""
    level: LogLevel = LogLevel.INFO
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    file: str = "logs/pdf_translator.log"
    rotation: str = "10 MB"
    retention: str = "7 days"


class PerformanceConfig(BaseModel):
    """性能配置。"""
    max_memory_usage: str = "2GB"
    timeout: int = Field(300, ge=30, description="超时时间（秒）")


class SecurityConfig(BaseModel):
    """安全配置。"""
    max_file_size: str = "100MB"
    allowed_extensions: List[str] = [".pdf"]
    scan_uploads: bool = True


class Settings(BaseSettings):
    """应用程序设置，支持从环境变量加载。"""
    
    # 基础配置
    app: AppConfig = AppConfig()
    translation: TranslationConfig = TranslationConfig()
    llm: LLMSettings = LLMSettings()
    pdf: PDFConfig = PDFConfig()
    image: ImageConfig = ImageConfig()
    cache: CacheConfig = CacheConfig()
    logging: LoggingConfig = LoggingConfig()
    performance: PerformanceConfig = PerformanceConfig()
    security: SecurityConfig = SecurityConfig()
    
    # 环境变量
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    debug: bool = False
    log_level: LogLevel = LogLevel.INFO
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def model_post_init(self, __context) -> None:
        """模型初始化后的处理。"""
        # 将环境变量中的API密钥设置到LLM配置中
        if self.openai_api_key:
            self.llm.openai.api_key = self.openai_api_key
        if self.anthropic_api_key:
            self.llm.anthropic.api_key = self.anthropic_api_key
            
        # 同步调试和日志级别设置
        if self.debug:
            self.app.debug = True
            self.app.log_level = LogLevel.DEBUG
            self.logging.level = LogLevel.DEBUG
        else:
            self.app.log_level = self.log_level
            self.logging.level = self.log_level


# 全局设置实例
settings = Settings()
