"""
日志工具模块。

提供统一的日志配置和管理功能。
"""

import sys
from pathlib import Path
from typing import Optional

from loguru import logger

from ..models.config import settings


def setup_logger(
    log_file: Optional[str] = None,
    log_level: Optional[str] = None,
    rotation: Optional[str] = None,
    retention: Optional[str] = None,
) -> None:
    """
    设置日志配置。
    
    Args:
        log_file: 日志文件路径
        log_level: 日志级别
        rotation: 日志轮转设置
        retention: 日志保留设置
    """
    # 移除默认处理器
    logger.remove()
    
    # 使用配置或默认值
    log_file = log_file or settings.logging.file
    log_level = log_level or settings.logging.level.value
    rotation = rotation or settings.logging.rotation
    retention = retention or settings.logging.retention
    format_str = settings.logging.format
    
    # 添加控制台处理器
    logger.add(
        sys.stderr,
        format=format_str,
        level=log_level,
        colorize=True,
    )
    
    # 添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            format=format_str,
            level=log_level,
            rotation=rotation,
            retention=retention,
            encoding="utf-8",
        )


def get_logger(name: str):
    """
    获取指定名称的日志器。
    
    Args:
        name: 日志器名称
        
    Returns:
        配置好的日志器实例
    """
    return logger.bind(name=name)


# 初始化日志配置
setup_logger()
