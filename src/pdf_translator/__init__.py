"""
PDF Translator - AI-powered PDF translation with layout preservation.

This package provides intelligent PDF translation capabilities using LangChain
and LangGraph for AI-driven translation workflows, PyMuPDF for PDF processing,
and advanced layout preservation techniques.
"""

__version__ = "0.1.0"
__author__ = "PDF Translator Team"
__email__ = "<EMAIL>"

from .core.translator import PDFTranslator
from .models.config import TranslationConfig
from .models.document import Document, TranslationResult

__all__ = [
    "PDFTranslator",
    "TranslationConfig", 
    "Document",
    "TranslationResult",
]
