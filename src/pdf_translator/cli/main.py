"""
PDF Translator 命令行接口。

提供用户友好的命令行工具来执行PDF翻译任务。
"""

from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

from ..models.config import LanguageCode, settings
from ..utils.logger import get_logger

# 创建 Typer 应用
app = typer.Typer(
    name="pdf-translator",
    help="AI-powered PDF translator with layout preservation",
    add_completion=False,
)

# 创建控制台和日志器
console = Console()
logger = get_logger(__name__)


@app.command()
def translate(
    input_file: Path = typer.Argument(
        ..., 
        help="输入PDF文件路径",
        exists=True,
        file_okay=True,
        dir_okay=False,
    ),
    output_file: Optional[Path] = typer.Option(
        None,
        "--output", "-o",
        help="输出PDF文件路径（默认为输入文件名_translated.pdf）"
    ),
    source_lang: LanguageCode = typer.Option(
        LanguageCode.AUTO,
        "--source", "-s",
        help="源语言代码"
    ),
    target_lang: LanguageCode = typer.Option(
        LanguageCode.ZH,
        "--target", "-t", 
        help="目标语言代码"
    ),
    config_file: Optional[Path] = typer.Option(
        None,
        "--config", "-c",
        help="自定义配置文件路径"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose", "-v",
        help="显示详细输出"
    ),
) -> None:
    """翻译单个PDF文件。"""
    
    # 设置日志级别
    if verbose:
        logger.info("启用详细输出模式")
    
    # 确定输出文件路径
    if output_file is None:
        output_file = input_file.parent / f"{input_file.stem}_translated.pdf"
    
    console.print(f"[bold green]PDF Translator v{settings.app.version}[/bold green]")
    console.print(f"输入文件: {input_file}")
    console.print(f"输出文件: {output_file}")
    console.print(f"翻译方向: {source_lang.value} → {target_lang.value}")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # 这里将来会调用实际的翻译逻辑
            task = progress.add_task("正在翻译PDF文件...", total=None)
            
            # TODO: 实现实际的翻译逻辑
            # from ..core.translator import PDFTranslator
            # translator = PDFTranslator(
            #     source_lang=source_lang,
            #     target_lang=target_lang,
            #     config_file=config_file
            # )
            # result = translator.translate_file(input_file, output_file)
            
            progress.update(task, description="翻译完成!")
            
        console.print(f"[bold green]✓[/bold green] 翻译完成: {output_file}")
        
    except Exception as e:
        console.print(f"[bold red]✗[/bold red] 翻译失败: {e}")
        logger.error(f"翻译失败: {e}")
        raise typer.Exit(1)


@app.command()
def batch(
    input_dir: Path = typer.Argument(
        ...,
        help="输入目录路径",
        exists=True,
        file_okay=False,
        dir_okay=True,
    ),
    output_dir: Optional[Path] = typer.Option(
        None,
        "--output", "-o",
        help="输出目录路径（默认为输入目录/translated）"
    ),
    source_lang: LanguageCode = typer.Option(
        LanguageCode.AUTO,
        "--source", "-s",
        help="源语言代码"
    ),
    target_lang: LanguageCode = typer.Option(
        LanguageCode.ZH,
        "--target", "-t",
        help="目标语言代码"
    ),
    pattern: str = typer.Option(
        "*.pdf",
        "--pattern", "-p",
        help="文件匹配模式"
    ),
    max_concurrent: int = typer.Option(
        3,
        "--concurrent", "-j",
        help="最大并发数"
    ),
) -> None:
    """批量翻译PDF文件。"""
    
    # 确定输出目录
    if output_dir is None:
        output_dir = input_dir / "translated"
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    # 查找PDF文件
    pdf_files = list(input_dir.glob(pattern))
    
    if not pdf_files:
        console.print(f"[yellow]警告:[/yellow] 在 {input_dir} 中未找到匹配 {pattern} 的文件")
        return
    
    console.print(f"[bold green]批量翻译模式[/bold green]")
    console.print(f"输入目录: {input_dir}")
    console.print(f"输出目录: {output_dir}")
    console.print(f"找到 {len(pdf_files)} 个PDF文件")
    console.print(f"翻译方向: {source_lang.value} → {target_lang.value}")
    console.print(f"并发数: {max_concurrent}")
    
    # TODO: 实现批量翻译逻辑
    console.print("[yellow]批量翻译功能正在开发中...[/yellow]")


@app.command()
def config(
    show: bool = typer.Option(
        False,
        "--show",
        help="显示当前配置"
    ),
    validate: bool = typer.Option(
        False,
        "--validate",
        help="验证配置文件"
    ),
) -> None:
    """配置管理。"""
    
    if show:
        console.print("[bold blue]当前配置:[/bold blue]")
        
        # 创建配置表格
        table = Table(title="PDF Translator 配置")
        table.add_column("配置项", style="cyan")
        table.add_column("值", style="green")
        
        table.add_row("应用版本", settings.app.version)
        table.add_row("调试模式", str(settings.app.debug))
        table.add_row("日志级别", settings.app.log_level.value)
        table.add_row("默认源语言", settings.translation.default_source_lang.value)
        table.add_row("默认目标语言", settings.translation.default_target_lang.value)
        table.add_row("LLM提供商", settings.llm.default_provider.value)
        table.add_row("OpenAI模型", settings.llm.openai.model)
        table.add_row("Anthropic模型", settings.llm.anthropic.model)
        
        console.print(table)
    
    if validate:
        console.print("[bold blue]验证配置...[/bold blue]")
        try:
            # 验证配置
            console.print("[green]✓[/green] 配置验证通过")
        except Exception as e:
            console.print(f"[red]✗[/red] 配置验证失败: {e}")


@app.command()
def version() -> None:
    """显示版本信息。"""
    console.print(f"PDF Translator v{settings.app.version}")
    console.print("AI-powered PDF translator with layout preservation")


if __name__ == "__main__":
    app()
