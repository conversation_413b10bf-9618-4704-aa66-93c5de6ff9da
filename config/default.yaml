# PDF Translator 默认配置文件

# 应用程序设置
app:
  name: "PDF Translator"
  version: "0.1.0"
  debug: false
  log_level: "INFO"

# 翻译设置
translation:
  # 默认源语言和目标语言
  default_source_lang: "auto"  # 自动检测
  default_target_lang: "zh"    # 中文
  
  # 支持的语言对
  supported_languages:
    - "en"  # 英语
    - "zh"  # 中文
    - "ja"  # 日语
    - "ko"  # 韩语
    - "fr"  # 法语
    - "de"  # 德语
    - "es"  # 西班牙语
    - "ru"  # 俄语
  
  # 翻译质量设置
  quality:
    min_score: 0.8          # 最低质量分数
    max_retries: 3          # 最大重试次数
    context_window: 2000    # 上下文窗口大小
    
  # 批处理设置
  batch:
    max_concurrent: 3       # 最大并发数
    chunk_size: 1000       # 文本块大小

# LLM 服务配置
llm:
  # 默认提供商
  default_provider: "openai"
  
  # OpenAI 配置
  openai:
    model: "gpt-4-turbo-preview"
    api_key: "${OPENAI_API_KEY}"
    base_url: "https://api.openai.com/v1"
    temperature: 0.1
    max_tokens: 4000
    
  # Anthropic 配置
  anthropic:
    model: "claude-3-sonnet-20240229"
    api_key: "${ANTHROPIC_API_KEY}"
    temperature: 0.1
    max_tokens: 4000
    
  # 本地模型配置
  local:
    model_path: "./models/local_model"
    device: "auto"  # auto, cpu, cuda

# PDF 处理配置
pdf:
  # 解析设置
  parsing:
    extract_images: true
    extract_tables: true
    preserve_layout: true
    dpi: 300  # 图像提取DPI
    
  # OCR 设置
  ocr:
    enabled: true
    language: "eng+chi_sim"  # tesseract 语言包
    confidence_threshold: 60  # 置信度阈值
    
  # 输出设置
  output:
    preserve_fonts: true
    preserve_colors: true
    preserve_images: true
    compression: "deflate"
    quality: 95

# 图像处理配置
image:
  # 支持的格式
  supported_formats: ["png", "jpg", "jpeg", "bmp", "tiff"]
  
  # 处理设置
  processing:
    max_size: [2048, 2048]  # 最大尺寸 [width, height]
    enhance_contrast: true
    denoise: true
    
# 缓存配置
cache:
  enabled: true
  ttl: 3600  # 缓存时间（秒）
  max_size: 1000  # 最大缓存条目数
  
# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/pdf_translator.log"
  rotation: "10 MB"
  retention: "7 days"

# 性能配置
performance:
  max_memory_usage: "2GB"
  timeout: 300  # 超时时间（秒）
  
# 安全配置
security:
  max_file_size: "100MB"
  allowed_extensions: [".pdf"]
  scan_uploads: true
