# PDF Translator

一个基于AI的智能PDF翻译器，支持保持原文档格式的高质量翻译。

## 🚀 特性

- **智能翻译**: 基于LangChain + LangGraph的AI翻译引擎
- **格式保持**: 保持原PDF的布局、字体、图像等格式
- **多语言支持**: 支持多种语言间的互译
- **OCR识别**: 自动识别图像中的文字并翻译
- **批量处理**: 支持批量翻译多个PDF文件
- **可配置**: 支持多种翻译服务提供商

## 🏗️ 系统架构

### 核心模块设计

```
pdf-translator/
├── src/
│   ├── pdf_translator/
│   │   ├── __init__.py
│   │   ├── core/                    # 核心业务逻辑
│   │   │   ├── __init__.py
│   │   │   ├── pdf_parser.py        # PDF解析器
│   │   │   ├── translator.py        # AI翻译引擎
│   │   │   ├── image_processor.py   # 图像处理
│   │   │   └── pdf_builder.py       # PDF重构器
│   │   ├── agents/                  # LangGraph智能体
│   │   │   ├── __init__.py
│   │   │   ├── translation_agent.py # 翻译智能体
│   │   │   ├── layout_agent.py      # 布局分析智能体
│   │   │   └── quality_agent.py     # 质量检查智能体
│   │   ├── models/                  # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── document.py          # 文档模型
│   │   │   ├── translation.py       # 翻译模型
│   │   │   └── config.py            # 配置模型
│   │   ├── services/                # 外部服务
│   │   │   ├── __init__.py
│   │   │   ├── llm_service.py       # LLM服务
│   │   │   ├── ocr_service.py       # OCR服务
│   │   │   └── storage_service.py   # 存储服务
│   │   ├── utils/                   # 工具函数
│   │   │   ├── __init__.py
│   │   │   ├── logger.py            # 日志工具
│   │   │   ├── file_utils.py        # 文件工具
│   │   │   └── image_utils.py       # 图像工具
│   │   └── cli/                     # 命令行接口
│   │       ├── __init__.py
│   │       └── main.py              # CLI主程序
├── tests/                           # 测试文件
├── docs/                            # 文档
├── examples/                        # 示例文件
├── config/                          # 配置文件
│   └── default.yaml
├── pyproject.toml                   # 项目配置
└── README.md                        # 项目说明
```

### 数据流设计

```mermaid
graph TD
    A[输入PDF] --> B[PDF解析器]
    B --> C[文本提取]
    B --> D[图像提取]
    B --> E[布局分析]
    
    C --> F[LangGraph翻译流程]
    D --> G[OCR文字识别]
    G --> F
    E --> H[布局智能体]
    
    F --> I[翻译智能体]
    I --> J[质量检查智能体]
    J --> K[翻译结果]
    
    K --> L[PDF重构器]
    H --> L
    L --> M[输出PDF]
```

## 🔧 技术栈

- **Python 3.12+**: 现代Python特性支持
- **uv**: 快速的Python包管理器
- **PyMuPDF (fitz)**: PDF解析和操作
- **LangChain**: AI应用开发框架
- **LangGraph**: 多智能体工作流编排
- **Pillow**: 图像处理
- **pytesseract**: OCR文字识别
- **Pydantic**: 数据验证和设置管理
- **Click**: 命令行界面
- **loguru**: 现代日志库

## 🚦 核心工作流程

### 1. PDF解析阶段
- 使用PyMuPDF解析PDF文档结构
- 提取文本块、图像、表格等元素
- 分析页面布局和元素位置关系
- 识别文档语言和编码

### 2. 内容预处理
- 文本清理和标准化
- 图像中文字的OCR识别
- 内容分块和上下文关联
- 翻译优先级排序

### 3. AI翻译流程 (LangGraph)
```python
# 翻译工作流示例
class TranslationWorkflow:
    def __init__(self):
        self.layout_agent = LayoutAnalysisAgent()
        self.translation_agent = TranslationAgent()
        self.quality_agent = QualityAssuranceAgent()
    
    def process(self, document):
        # 布局分析
        layout_info = self.layout_agent.analyze(document)
        
        # 智能翻译
        translated_content = self.translation_agent.translate(
            document.content, 
            context=layout_info
        )
        
        # 质量检查
        final_result = self.quality_agent.review(
            original=document.content,
            translated=translated_content,
            layout=layout_info
        )
        
        return final_result
```

### 4. PDF重构阶段
- 根据原始布局重新排版
- 保持字体、颜色、样式
- 处理翻译后文本长度变化
- 生成最终PDF文档

## 🎯 智能体设计

### 布局分析智能体 (Layout Agent)
- **职责**: 分析PDF布局结构
- **输入**: PDF页面元素
- **输出**: 布局信息和翻译策略
- **特点**: 理解文档结构，优化翻译顺序

### 翻译智能体 (Translation Agent)
- **职责**: 执行智能翻译任务
- **输入**: 文本内容 + 上下文信息
- **输出**: 高质量翻译结果
- **特点**: 上下文感知，术语一致性

### 质量检查智能体 (Quality Agent)
- **职责**: 翻译质量评估和优化
- **输入**: 原文 + 译文 + 布局信息
- **输出**: 质量评分和改进建议
- **特点**: 多维度质量评估

## 📋 配置管理

支持灵活的配置管理，包括：

- **翻译服务配置**: OpenAI, Anthropic, 本地模型等
- **语言对配置**: 支持的翻译语言组合
- **质量参数**: 翻译质量阈值和策略
- **输出格式**: PDF样式和布局选项

## 🔄 使用流程

### 命令行使用
```bash
# 基本翻译
pdf-translator translate input.pdf --target-lang zh --output output.pdf

# 批量翻译
pdf-translator batch-translate ./pdfs/ --target-lang en --output-dir ./translated/

# 自定义配置
pdf-translator translate input.pdf --config custom_config.yaml
```

### Python API使用
```python
from pdf_translator import PDFTranslator

translator = PDFTranslator(
    source_lang="en",
    target_lang="zh",
    model="gpt-4"
)

result = translator.translate_file("input.pdf", "output.pdf")
print(f"翻译完成: {result.status}")
```

## 🎨 特色功能

1. **智能布局保持**: 自动适应翻译后文本长度变化
2. **上下文感知翻译**: 基于文档上下文提供准确翻译
3. **多模态处理**: 同时处理文本和图像内容
4. **质量保证**: 多层质量检查确保翻译准确性
5. **可扩展架构**: 支持添加新的翻译服务和功能

## 🚀 快速开始

详细的安装和使用说明请参考 [快速开始指南](docs/quickstart.md)。

## 📚 文档

- [API文档](docs/api.md)
- [配置指南](docs/configuration.md)
- [开发指南](docs/development.md)
- [常见问题](docs/faq.md)

## 🤝 贡献

欢迎贡献代码！请查看 [贡献指南](docs/contributing.md)。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。
