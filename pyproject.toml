[project]
name = "pdf-translator"
version = "0.1.0"
description = "AI-powered PDF translator with layout preservation"
authors = [
    {name = "PDF Translator Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.12"
keywords = ["pdf", "translation", "ai", "langchain", "langgraph"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Text Processing :: Linguistic",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "langchain>=0.1.0",
    "langgraph>=0.0.40",
    "langchain-openai>=0.1.0",
    "langchain-anthropic>=0.1.0",
    "pymupdf>=1.23.0",
    "pillow>=10.0.0",
    "pytesseract>=0.3.10",
    "pydantic>=2.5.0",
    "click>=8.1.0",
    "loguru>=0.7.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "rich>=13.0.0",
    "typer>=0.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
]

docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocstrings[python]>=0.22.0",
]

[project.scripts]
pdf-translator = "pdf_translator.cli.main:app"

[project.urls]
Homepage = "https://github.com/your-username/pdf-translator"
Documentation = "https://pdf-translator.readthedocs.io"
Repository = "https://github.com/your-username/pdf-translator"
Issues = "https://github.com/your-username/pdf-translator/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/pdf_translator"]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["pdf_translator"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/pdf_translator",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "-v"
]

[tool.coverage.run]
source = ["src/pdf_translator"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
