# PDF Translator 环境变量配置示例
# 复制此文件为 .env 并填入实际的API密钥

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic API 配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 其他LLM服务配置
# AZURE_OPENAI_API_KEY=your_azure_openai_key
# AZURE_OPENAI_ENDPOINT=your_azure_endpoint
# GOOGLE_API_KEY=your_google_api_key

# 应用配置
DEBUG=false
LOG_LEVEL=INFO

# 数据库配置（如果需要）
# DATABASE_URL=sqlite:///./pdf_translator.db

# 缓存配置
# REDIS_URL=redis://localhost:6379/0

# 文件存储配置
UPLOAD_DIR=./uploads
OUTPUT_DIR=./outputs
TEMP_DIR=./temp

# 安全配置
SECRET_KEY=your_secret_key_here
MAX_FILE_SIZE=104857600  # 100MB in bytes

# OCR 配置
TESSERACT_CMD=/usr/bin/tesseract  # tesseract 可执行文件路径
