# 快速开始指南

本指南将帮助您快速设置和使用PDF Translator。

## 系统要求

- Python 3.12 或更高版本
- uv 包管理器
- Tesseract OCR（用于图像文字识别）

## 安装

### 1. 克隆项目

```bash
git clone https://github.com/your-username/pdf-translator.git
cd pdf-translator
```

### 2. 安装依赖

使用 uv 安装项目依赖：

```bash
# 安装基础依赖
uv sync

# 安装开发依赖
uv sync --extra dev

# 安装所有依赖
uv sync --all-extras
```

### 3. 安装 Tesseract OCR

#### macOS
```bash
brew install tesseract
brew install tesseract-lang  # 多语言支持
```

#### Ubuntu/Debian
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-chi-sim  # 中文支持
sudo apt-get install tesseract-ocr-eng      # 英文支持
```

#### Windows
下载并安装 [Tesseract for Windows](https://github.com/UB-Mannheim/tesseract/wiki)

### 4. 配置环境变量

复制环境变量模板并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，添加您的API密钥：

```env
# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API 配置  
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

## 基本使用

### 命令行工具

#### 翻译单个PDF文件

```bash
# 基本翻译（英文到中文）
uv run pdf-translator translate input.pdf

# 指定输出文件
uv run pdf-translator translate input.pdf --output translated.pdf

# 指定语言对
uv run pdf-translator translate input.pdf --source en --target zh

# 使用自定义配置
uv run pdf-translator translate input.pdf --config custom_config.yaml
```

#### 批量翻译

```bash
# 批量翻译目录中的所有PDF文件
uv run pdf-translator batch ./pdfs/ --output-dir ./translated/

# 指定文件模式和并发数
uv run pdf-translator batch ./pdfs/ --pattern "*.pdf" --concurrent 5
```

#### 查看配置

```bash
# 显示当前配置
uv run pdf-translator config --show

# 验证配置
uv run pdf-translator config --validate
```

### Python API

```python
from pdf_translator import PDFTranslator

# 创建翻译器实例
translator = PDFTranslator(
    source_lang="en",
    target_lang="zh", 
    model="gpt-4-turbo-preview"
)

# 翻译文件
result = translator.translate_file("input.pdf", "output.pdf")

# 检查结果
if result.success:
    print(f"翻译完成: {result.output_file}")
    print(f"质量分数: {result.quality_score}")
else:
    print(f"翻译失败: {result.error}")
```

## 配置说明

### 支持的语言

- `en` - 英语
- `zh` - 中文
- `ja` - 日语
- `ko` - 韩语
- `fr` - 法语
- `de` - 德语
- `es` - 西班牙语
- `ru` - 俄语
- `auto` - 自动检测（仅限源语言）

### LLM 提供商

#### OpenAI
```yaml
llm:
  openai:
    model: "gpt-4-turbo-preview"
    api_key: "${OPENAI_API_KEY}"
    temperature: 0.1
    max_tokens: 4000
```

#### Anthropic
```yaml
llm:
  anthropic:
    model: "claude-3-sonnet-20240229"
    api_key: "${ANTHROPIC_API_KEY}"
    temperature: 0.1
    max_tokens: 4000
```

### 质量控制

```yaml
translation:
  quality:
    min_score: 0.8      # 最低质量分数
    max_retries: 3      # 最大重试次数
    context_window: 2000 # 上下文窗口大小
```

## 故障排除

### 常见问题

1. **Tesseract 未找到**
   ```
   Error: Tesseract not found
   ```
   解决方案：确保 Tesseract 已正确安装并在 PATH 中。

2. **API 密钥错误**
   ```
   Error: Invalid API key
   ```
   解决方案：检查 `.env` 文件中的 API 密钥是否正确。

3. **内存不足**
   ```
   Error: Out of memory
   ```
   解决方案：减少并发数或增加系统内存。

### 日志调试

启用详细日志：

```bash
# 命令行工具
uv run pdf-translator translate input.pdf --verbose

# 或设置环境变量
export LOG_LEVEL=DEBUG
uv run pdf-translator translate input.pdf
```

查看日志文件：

```bash
tail -f logs/pdf_translator.log
```

## 下一步

- 查看 [API 文档](api.md) 了解详细的编程接口
- 阅读 [配置指南](configuration.md) 了解高级配置选项
- 参考 [开发指南](development.md) 了解如何贡献代码

## 获取帮助

如果您遇到问题，可以：

1. 查看 [常见问题](faq.md)
2. 在 [GitHub Issues](https://github.com/your-username/pdf-translator/issues) 中提交问题
3. 参与 [讨论区](https://github.com/your-username/pdf-translator/discussions)
